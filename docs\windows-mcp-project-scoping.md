# Windows MCP Project-Specific Scoping Guide

## Overview

The Windows TaskMaster MCP now operates in **project-specific mode**, showing only tasks from your current workspace while maintaining complete isolation from the WSL global instance.

## Key Features

### 🎯 **Project-Specific Task Visibility**
- Only see tasks from your current project/workspace
- No cross-project task contamination
- Automatic workspace detection from Claude <PERSON>/Cursor

### 🔒 **Complete Isolation**
- Separate from WSL TaskMaster MCP (if you have one)
- Independent configuration per project
- No interference between different projects

### ⚙️ **Automatic Configuration**
- Environment-based mode detection
- Session-based workspace identification
- Project-specific `.taskmaster/config.json` creation

## How It Works

### Automatic Mode Detection
```
Windows Environment → Project-Specific Mode
WSL/Linux Environment → Global Mode (unchanged)
```

### Project Boundary Enforcement
- All tasks stored in `{workspace}/.taskmaster/tasks/tasks.json`
- Configuration in `{workspace}/.taskmaster/config.json`
- Cross-project access automatically prevented

### Workspace Detection
1. **Primary**: <PERSON> workspace root
2. **Secondary**: Cursor project directory
3. **Fallback**: Current working directory

## Setup Instructions

### 1. <PERSON> Configuration
Add to your Claude Desktop MCP configuration:

```json
{
  "mcpServers": {
    "taskmaster-windows": {
      "command": "node",
      "args": [
        "C:\\Users\\<USER>\\Documents\\Cline\\MCP\\task-master-ai\\mcp-server\\server.js"
      ],
      "env": {
        "ANTHROPIC_API_KEY": "your-anthropic-key",
        "OPENROUTER_API_KEY": "your-openrouter-key",
        "PERPLEXITY_API_KEY": "your-perplexity-key"
      },
      "autoApprove": [],
      "disabled": false,
      "timeout": 60,
      "transportType": "stdio"
    }
  }
}
```

### 2. Project Initialization
When you first use TaskMaster in a new project:

1. **Open your project** in Claude Desktop/Cursor
2. **Use any TaskMaster command** (e.g., "initialize project")
3. **Project structure created automatically**:
   ```
   your-project/
   ├── .taskmaster/
   │   ├── config.json
   │   ├── tasks/
   │   │   └── tasks.json
   │   ├── docs/
   │   ├── reports/
   │   └── templates/
   ```

### 3. Model Configuration
Configure AI models for your project:

```json
{
  "models": {
    "main": {
      "provider": "anthropic",
      "modelId": "claude-3-5-sonnet-20241022",
      "maxTokens": 64000,
      "temperature": 0.2
    },
    "research": {
      "provider": "perplexity",
      "modelId": "llama-3.1-sonar-large-128k-online",
      "maxTokens": 8700,
      "temperature": 0.1
    },
    "fallback": {
      "provider": "openrouter",
      "modelId": "anthropic/claude-3.5-sonnet",
      "maxTokens": 8192,
      "temperature": 0.2
    }
  },
  "global": {
    "projectName": "My Project",
    "logLevel": "info",
    "defaultPriority": "medium"
  }
}
```

## Usage Examples

### Basic Workflow
1. **Open project** in Claude Desktop
2. **Initialize**: "Initialize TaskMaster project"
3. **Parse requirements**: "Parse this PRD file: requirements.txt"
4. **View tasks**: "List all tasks"
5. **Work on tasks**: "Show me the next task to work on"

### Project Switching
- **Switch projects** by opening different workspaces in Claude Desktop
- **Tasks automatically scoped** to current workspace
- **No manual configuration** required

### Multiple Projects
```
Project A (Workspace 1)
├── .taskmaster/tasks/tasks.json  ← Only Project A tasks
└── .taskmaster/config.json       ← Project A configuration

Project B (Workspace 2)  
├── .taskmaster/tasks/tasks.json  ← Only Project B tasks
└── .taskmaster/config.json       ← Project B configuration
```

## Troubleshooting

### Issue: Tasks from other projects showing up
**Solution**: Ensure you're in the correct workspace in Claude Desktop

### Issue: No tasks visible
**Solution**: 
1. Check if `.taskmaster/` directory exists in project root
2. Initialize project if needed
3. Verify workspace detection in logs

### Issue: Configuration not loading
**Solution**:
1. Check `.taskmaster/config.json` exists
2. Verify JSON syntax is valid
3. Restart Claude Desktop if needed

### Issue: Cross-project access errors
**Solution**: This is expected behavior - project boundaries are enforced

## Logging and Debugging

### Server Logs
When starting, you'll see:
```
[TaskMaster] Windows MCP - Project-specific mode enabled
```

### Project Detection Logs
```
[PROJECT MODE] Found workspace root URI: file:///C:/path/to/project
Using project root from project mode session: C:\path\to\project
```

### Boundary Enforcement Logs
```
Enforcing project boundaries for: C:\path\to\project
Access denied: C:\other\project\tasks.json is outside project boundary
```

## Benefits

### ✅ **Clean Separation**
- Each project has its own task space
- No task contamination between projects
- Clear project boundaries

### ✅ **Flexible Configuration**
- Different AI models per project
- Project-specific settings
- Independent API key management

### ✅ **Seamless Integration**
- Works with Claude Desktop workspace switching
- Automatic project detection
- No manual configuration required

### ✅ **Maintained Compatibility**
- WSL TaskMaster MCP unchanged (if you have one)
- All existing TaskMaster features available
- Backward compatible with existing workflows

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review server logs for error messages
3. Verify workspace detection is working correctly
4. Ensure project structure is properly initialized
